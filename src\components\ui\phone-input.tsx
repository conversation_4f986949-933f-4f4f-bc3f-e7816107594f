"use client";

import React, { useState, useRef, useEffect } from "react";
import { ChevronDown } from "lucide-react";
import { countryCodes, defaultCountryCode, CountryCode } from "@/data/country-codes";

interface PhoneInputProps {
    value: string;
    onChange: (value: string) => void;
    placeholder?: string;
    className?: string;
    disabled?: boolean;
    error?: boolean;
    name?: string;
}

export const PhoneInput: React.FC<PhoneInputProps> = ({
    value,
    onChange,
    placeholder = "Phone number",
    className = "",
    disabled = false,
    error = false,
    name = "phone"
}) => {
    const [selectedCountryCode, setSelectedCountryCode] = useState<string>(defaultCountryCode);
    const [phoneNumber, setPhoneNumber] = useState<string>("");
    const [isDropdownOpen, setIsDropdownOpen] = useState(false);
    const [searchTerm, setSearchTerm] = useState("");
    const dropdownRef = useRef<HTMLDivElement>(null);
    const searchInputRef = useRef<HTMLInputElement>(null);

    // Initialize values from prop
    useEffect(() => {
        if (value) {
            // Try to extract country code from the value
            const matchedCountry = countryCodes.find(country => 
                value.startsWith(country.code)
            );
            
            if (matchedCountry) {
                setSelectedCountryCode(matchedCountry.code);
                setPhoneNumber(value.substring(matchedCountry.code.length));
            } else {
                setPhoneNumber(value);
            }
        }
    }, [value]);

    // Update parent component when values change
    useEffect(() => {
        const fullPhoneNumber = phoneNumber ? `${selectedCountryCode}${phoneNumber}` : "";
        if (fullPhoneNumber !== value) {
            onChange(fullPhoneNumber);
        }
    }, [selectedCountryCode, phoneNumber, onChange, value]);

    // Close dropdown when clicking outside
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
                setIsDropdownOpen(false);
                setSearchTerm("");
            }
        };

        document.addEventListener("mousedown", handleClickOutside);
        return () => document.removeEventListener("mousedown", handleClickOutside);
    }, []);

    // Focus search input when dropdown opens
    useEffect(() => {
        if (isDropdownOpen && searchInputRef.current) {
            searchInputRef.current.focus();
        }
    }, [isDropdownOpen]);

    const handleCountrySelect = (countryCode: string) => {
        setSelectedCountryCode(countryCode);
        setIsDropdownOpen(false);
        setSearchTerm("");
    };

    const handlePhoneNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const newValue = e.target.value.replace(/[^\d]/g, ""); // Only allow digits
        setPhoneNumber(newValue);
    };

    const filteredCountries = countryCodes.filter(country =>
        country.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        country.code.includes(searchTerm)
    );

    const selectedCountry = countryCodes.find(country => country.code === selectedCountryCode);

    const baseClasses = "flex border rounded-md focus-within:ring-1 transition-all duration-200";
    const errorClasses = error 
        ? "border-red-500 focus-within:border-red-500 focus-within:ring-red-500" 
        : "border-gray-300 focus-within:border-[#a5cd39] focus-within:ring-[#a5cd39]";
    const disabledClasses = disabled ? "opacity-50 cursor-not-allowed" : "";

    return (
        <div className={`relative ${className}`}>
            <div className={`${baseClasses} ${errorClasses} ${disabledClasses}`}>
                {/* Country Code Selector */}
                <div className="relative" ref={dropdownRef}>
                    <button
                        type="button"
                        onClick={() => !disabled && setIsDropdownOpen(!isDropdownOpen)}
                        disabled={disabled}
                        className="flex items-center px-3 py-2 border-r border-gray-300 bg-gray-50 hover:bg-gray-100 transition-colors duration-200 disabled:cursor-not-allowed disabled:hover:bg-gray-50"
                    >
                        <span className="mr-1">{selectedCountry?.flag}</span>
                        <span className="text-sm font-medium">{selectedCountryCode}</span>
                        <ChevronDown className="ml-1 h-4 w-4 text-gray-500" />
                    </button>

                    {/* Dropdown */}
                    {isDropdownOpen && (
                        <div className="absolute top-full left-0 mt-1 w-80 bg-white border border-gray-300 rounded-md shadow-lg z-50 max-h-60 overflow-hidden">
                            {/* Search Input */}
                            <div className="p-2 border-b border-gray-200">
                                <input
                                    ref={searchInputRef}
                                    type="text"
                                    placeholder="Search countries..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:border-[#a5cd39]"
                                />
                            </div>

                            {/* Country List */}
                            <div className="max-h-48 overflow-y-auto">
                                {filteredCountries.map((country) => (
                                    <button
                                        key={country.code}
                                        type="button"
                                        onClick={() => handleCountrySelect(country.code)}
                                        className="w-full flex items-center px-3 py-2 text-left hover:bg-gray-100 transition-colors duration-150"
                                    >
                                        <span className="mr-2">{country.flag}</span>
                                        <span className="flex-1 text-sm">{country.name}</span>
                                        <span className="text-sm font-medium text-gray-600">{country.code}</span>
                                    </button>
                                ))}
                                {filteredCountries.length === 0 && (
                                    <div className="px-3 py-2 text-sm text-gray-500">
                                        No countries found
                                    </div>
                                )}
                            </div>
                        </div>
                    )}
                </div>

                {/* Phone Number Input */}
                <input
                    type="tel"
                    name={name}
                    value={phoneNumber}
                    onChange={handlePhoneNumberChange}
                    placeholder={placeholder}
                    disabled={disabled}
                    className="flex-1 px-3 py-2 text-sm bg-transparent focus:outline-none placeholder:text-gray-500 disabled:cursor-not-allowed"
                />
            </div>
        </div>
    );
};

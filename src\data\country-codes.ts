export interface CountryCode {
    code: string;
    name: string;
    flag: string;
    iso2: string; // ISO 3166-1 alpha-2 country code for API matching
}

export const countryCodes: CountryCode[] = [
    // A
    { code: "+93", name: "Afghanistan", flag: "🇦🇫", iso2: "AF" },
    { code: "+355", name: "Albania", flag: "🇦�", iso2: "AL" },
    { code: "+213", name: "Algeria", flag: "🇩🇿", iso2: "DZ" },
    { code: "+1684", name: "American Samoa", flag: "�🇸", iso2: "AS" },
    { code: "+376", name: "Andorra", flag: "🇦🇩", iso2: "AD" },
    { code: "+244", name: "Angola", flag: "��", iso2: "AO" },
    { code: "+1264", name: "<PERSON><PERSON><PERSON>", flag: "��", iso2: "AI" },
    { code: "+1268", name: "Antigua and Barbuda", flag: "��", iso2: "AG" },
    { code: "+54", name: "Argentina", flag: "��", iso2: "AR" },
    { code: "+374", name: "Armenia", flag: "🇦🇲", iso2: "AM" },
    { code: "+297", name: "Aruba", flag: "��", iso2: "AW" },
    { code: "+61", name: "Australia", flag: "��", iso2: "AU" },
    { code: "+43", name: "Austria", flag: "��", iso2: "AT" },
    { code: "+994", name: "Azerbaijan", flag: "��", iso2: "AZ" },

    // B
    { code: "+1242", name: "Bahamas", flag: "��", iso2: "BS" },
    { code: "+973", name: "Bahrain", flag: "�🇭", iso2: "BH" },
    { code: "+880", name: "Bangladesh", flag: "�🇩", iso2: "BD" },
    { code: "+1246", name: "Barbados", flag: "🇧🇧", iso2: "BB" },
    { code: "+375", name: "Belarus", flag: "��", iso2: "BY" },
    { code: "+32", name: "Belgium", flag: "��", iso2: "BE" },
    { code: "+501", name: "Belize", flag: "��", iso2: "BZ" },
    { code: "+229", name: "Benin", flag: "��", iso2: "BJ" },
    { code: "+1441", name: "Bermuda", flag: "��", iso2: "BM" },
    { code: "+975", name: "Bhutan", flag: "��", iso2: "BT" },
    { code: "+591", name: "Bolivia", flag: "��", iso2: "BO" },
    { code: "+387", name: "Bosnia and Herzegovina", flag: "🇧�", iso2: "BA" },
    { code: "+267", name: "Botswana", flag: "��", iso2: "BW" },
    { code: "+55", name: "Brazil", flag: "�🇷", iso2: "BR" },
    { code: "+673", name: "Brunei", flag: "��", iso2: "BN" },
    { code: "+359", name: "Bulgaria", flag: "🇧🇬", iso2: "BG" },
    { code: "+226", name: "Burkina Faso", flag: "��", iso2: "BF" },
    { code: "+257", name: "Burundi", flag: "��", iso2: "BI" },

    // C
    { code: "+855", name: "Cambodia", flag: "��", iso2: "KH" },
    { code: "+237", name: "Cameroon", flag: "��", iso2: "CM" },
    { code: "+1", name: "Canada", flag: "��", iso2: "CA" },
    { code: "+238", name: "Cape Verde", flag: "��", iso2: "CV" },
    { code: "+1345", name: "Cayman Islands", flag: "🇰�", iso2: "KY" },
    { code: "+236", name: "Central African Republic", flag: "��", iso2: "CF" },
    { code: "+235", name: "Chad", flag: "🇹🇩", iso2: "TD" },
    { code: "+56", name: "Chile", flag: "��", iso2: "CL" },
    { code: "+86", name: "China", flag: "��", iso2: "CN" },
    { code: "+57", name: "Colombia", flag: "��", iso2: "CO" },
    { code: "+269", name: "Comoros", flag: "🇰�", iso2: "KM" },
    { code: "+242", name: "Congo", flag: "��", iso2: "CG" },
    { code: "+243", name: "Congo (DRC)", flag: "��", iso2: "CD" },
    { code: "+682", name: "Cook Islands", flag: "��", iso2: "CK" },
    { code: "+506", name: "Costa Rica", flag: "��", iso2: "CR" },
    { code: "+225", name: "Côte d'Ivoire", flag: "��", iso2: "CI" },
    { code: "+385", name: "Croatia", flag: "��", iso2: "HR" },
    { code: "+53", name: "Cuba", flag: "��", iso2: "CU" },
    { code: "+357", name: "Cyprus", flag: "��", iso2: "CY" },
    { code: "+420", name: "Czech Republic", flag: "��", iso2: "CZ" },

    // D
    { code: "+45", name: "Denmark", flag: "��", iso2: "DK" },
    { code: "+253", name: "Djibouti", flag: "��", iso2: "DJ" },
    { code: "+1767", name: "Dominica", flag: "��", iso2: "DM" },
    { code: "+1809", name: "Dominican Republic", flag: "��", iso2: "DO" },

    // E
    { code: "+593", name: "Ecuador", flag: "��", iso2: "EC" },
    { code: "+20", name: "Egypt", flag: "🇪🇬", iso2: "EG" },
    { code: "+503", name: "El Salvador", flag: "��", iso2: "SV" },
    { code: "+240", name: "Equatorial Guinea", flag: "��", iso2: "GQ" },
    { code: "+291", name: "Eritrea", flag: "��", iso2: "ER" },
    { code: "+372", name: "Estonia", flag: "🇪🇪", iso2: "EE" },
    { code: "+251", name: "Ethiopia", flag: "🇹", iso2: "ET" },

    // F
    { code: "+679", name: "Fiji", flag: "��", iso2: "FJ" },
    { code: "+358", name: "Finland", flag: "🇫🇮", iso2: "FI" },
    { code: "+33", name: "France", flag: "��", iso2: "FR" },
    { code: "+594", name: "French Guiana", flag: "��", iso2: "GF" },
    { code: "+689", name: "French Polynesia", flag: "��", iso2: "PF" },

    // G
    { code: "+241", name: "Gabon", flag: "��", iso2: "GA" },
    { code: "+220", name: "Gambia", flag: "��", iso2: "GM" },
    { code: "+995", name: "Georgia", flag: "🇪", iso2: "GE" },
    { code: "+49", name: "Germany", flag: "��", iso2: "DE" },
    { code: "+233", name: "Ghana", flag: "��", iso2: "GH" },
    { code: "+350", name: "Gibraltar", flag: "��", iso2: "GI" },
    { code: "+30", name: "Greece", flag: "��", iso2: "GR" },
    { code: "+299", name: "Greenland", flag: "🇬🇱", iso2: "GL" },
    { code: "+1473", name: "Grenada", flag: "��", iso2: "GD" },
    { code: "+590", name: "Guadeloupe", flag: "🇬🇵", iso2: "GP" },
    { code: "+1671", name: "Guam", flag: "��", iso2: "GU" },
    { code: "+502", name: "Guatemala", flag: "��", iso2: "GT" },
    { code: "+224", name: "Guinea", flag: "��", iso2: "GN" },
    { code: "+245", name: "Guinea-Bissau", flag: "��", iso2: "GW" },
    { code: "+592", name: "Guyana", flag: "��", iso2: "GY" }
];

// Default country code (UAE)
export const defaultCountryCode = "+971";
